from pydantic import BaseModel, ConfigDict
from datetime import datetime
from typing import Any
import uuid


class CustomerBase(BaseModel):
    name: str


class CustomerIn(CustomerBase):
    pass


class CustomerOut(CustomerBase):
    id: int
    model_config = ConfigDict(from_attributes=True)



class RouteBase(BaseModel):
    start_location_id: int
    start_location_name: str | None
    end_location_id: int
    end_location_name: str | None
    emergency_contact: str
    known_dangers: str | None
    extra_notes: str | None
    external_route_id: str | None = None
    customer_id: int | None

class RouteIn(RouteBase):
    pass


class RouteOut(RouteBase):
    id: int
    external_route_id: str  # This field will always be present in the response
    customer: CustomerOut | None
    model_config = ConfigDict(from_attributes=True)


class RouteUpdate(BaseModel):
    emergency_contact: str | None = None
    known_dangers: str | None = None
    extra_notes: str | None = None
    external_route_id: str | None = None


class RideStatusResponse(BaseModel):
    id: int
    name: str
    description: str | None
    model_config = ConfigDict(from_attributes=True)


class CancelReasonBase(BaseModel):
    name: str
    description: str | None

class CancelReasonIn(CancelReasonBase):
    pass

class CancelReasonOut(CancelReasonBase):
    id: int
    model_config = ConfigDict(from_attributes=True)


class RideBase(BaseModel):
    from_location: int | None = None
    to_location: int | None = None
    departure_time: datetime | None = None
    arrival_time: datetime | None = None
    ride_status_id: int | None = None
    glider_id: int | None = None
    glider_name: str | None = None
    operator_id: uuid.UUID | None = None
    has_package: bool = False
    package_description: str | None = None
    route_id: int | None = None
    cancel_reason_id: int | None = None

    def __init__(self, **data: Any):
        super().__init__(**data)


class RideIn(RideBase):
    pass


class RideOut(RideBase):
    id: int
    route: RouteOut | None
    ride_status: RideStatusResponse | None
    cancel_reason: CancelReasonOut | None
    model_config = ConfigDict(from_attributes=True)


class RideUpdate(BaseModel):
    ride_status_id: int | None = None
    has_package: bool | None = None
    package_description: str | None = None
    departure_time: datetime | None = None
    arrival_time: datetime | None = None
    glider_id: int | None = None
    operator_id: uuid.UUID | None = None
    glider_name: str | None = None
    route_id: int | None = None
    cancel_reason_id: int | None = None



class TimeTrackingEventBase(BaseModel):
    route_id: int
    pilot_email: str
    event: str
    shift_id: int
    event_timestamp: datetime | None = None

class TimeTrackingEventCreate(TimeTrackingEventBase):
    pass

class TimeTrackingEventOut(TimeTrackingEventBase):
    id: int
    created_at: datetime
    event_timestamp: datetime  # Always required in output
    model_config = ConfigDict(from_attributes=True)


class ShiftBase(BaseModel):
    pilot_email: str
    route_id: int
    start_time: datetime
    stop_time: datetime | None
    description: str | None
    ride_id: int | None
    created_at: datetime
    updated_at: datetime

class ShiftOut(ShiftBase):
    id: int
    model_config = ConfigDict(from_attributes=True)


class ShiftUpdate(BaseModel):
    pilot_email: str | None = None
    route_id: int | None = None
    start_time: datetime = None
    stop_time: datetime | None = None
    description: str | None = None
    ride_id: int | None = None
    model_config = ConfigDict(from_attributes=True)
