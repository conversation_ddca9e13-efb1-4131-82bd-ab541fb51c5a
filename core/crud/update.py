from sqlalchemy.orm import Session
from fastapi import HTTPException
from core.models.tables import Shift
from core.schemas.schemas import ShiftUpdate


def update_shift_table_entry(
    db: Session,
    shift_id: int,
    update_data: ShiftUpdate
) -> Shift:
    update_data_dict = update_data.model_dump(exclude_unset=True)
    if not update_data_dict:
        raise HTTPException(status_code=400, detail="No fields to update")
    
    shift = db.query(Shift).filter(Shift.id == shift_id).first()
    if not shift:
        raise HTTPException(status_code=404, detail="Shift not found")
    
    if update_data_dict.get("pilot_email") is not None:
        shift.pilot_email = update_data.pilot_email
    
    if update_data_dict.get("route_id") is not None:
        shift.route_id = update_data.route_id

    if update_data_dict.get("start_time") is not None:
        shift.start_time = update_data.start_time
    
    if update_data_dict.get("stop_time") is not None:
        shift.stop_time = update_data.stop_time
    
    if update_data_dict.get("description") is not None:
        shift.description = update_data.description

    if update_data_dict.get("ride_id") is not None:
        shift.ride_id = update_data.ride_id

    db.commit()
    db.refresh(shift)
    return shift
