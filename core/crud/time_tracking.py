from datetime import datetime
from sqlalchemy.orm import Session
from core.models.tables import TimeTrackingEvent, TimeTrackingEventType
from core.schemas.schemas import TimeTrackingEventCreate
from exceptions import InvalidTimeTrackingException

def create_time_tracking_event(db: Session, event: TimeTrackingEventCreate, with_commit: bool = True) -> TimeTrackingEvent:
    if event.event == TimeTrackingEventType.STOP:
        # Get the latest START event for this pilot and route
        start_event = db.query(TimeTrackingEvent)\
            .filter(
                TimeTrackingEvent.pilot_email == event.pilot_email,
                TimeTrackingEvent.route_id == event.route_id,
                TimeTrackingEvent.event == TimeTrackingEventType.START
            )\
            .order_by(TimeTrackingEvent.event_timestamp.desc())\
            .first()
        
        if not start_event:
            raise InvalidTimeTrackingException(
                "Timer was not even started in the first place"
            )

        if start_event and event.event_timestamp < start_event.event_timestamp:
            raise InvalidTimeTrackingException(
                "Stop time cannot be before start time"
            )

    db_event = TimeTrackingEvent(
        route_id=event.route_id,
        shift_id=event.shift_id,
        pilot_email=event.pilot_email,
        event=event.event,
        event_timestamp=event.event_timestamp or datetime.utcnow()
    )
    db.add(db_event)
    db.flush()
    if with_commit:
        db.commit()
        db.refresh(db_event)
    
    return db_event

def get_active_time_tracking(db: Session, shift_id: int) -> TimeTrackingEvent | None:
    # Get the latest event for this shift
    latest_event = db.query(TimeTrackingEvent)\
        .filter(TimeTrackingEvent.shift_id == shift_id)\
        .order_by(TimeTrackingEvent.event_timestamp.desc())\
        .first()
    
    if latest_event and latest_event.event == TimeTrackingEventType.START:
        return latest_event
    return None 

def get_active_time_tracking_by_pilot_email(db: Session, pilot_email: str) -> TimeTrackingEvent | None:
    # Get the latest event for this pilot
    latest_event = db.query(TimeTrackingEvent)\
        .filter(TimeTrackingEvent.pilot_email == pilot_email)\
        .order_by(TimeTrackingEvent.event_timestamp.desc())\
        .first()
    
    if latest_event and latest_event.event == TimeTrackingEventType.START:
        return latest_event
    return None