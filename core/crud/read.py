from sqlalchemy.orm import Session
from sqlalchemy import cast, Date
from datetime import datetime, date
import uuid
from core.models.tables import Ride, RideStatus, Route, Customer, Shift, CancelReason, TimeTrackingEvent

def read_all_cancel_reasons(db: Session) -> list[CancelReason]:
    return db.query(CancelReason).all()

def read_cancel_reason_by_id(db: Session, cancel_reason_id: int) -> CancelReason | None:
    return db.query(CancelReason).filter(CancelReason.id == cancel_reason_id).first()


def read_ride_by_id(db: Session, ride_id: int) -> Ride | None:
    return db.query(Ride).filter(Ride.id == ride_id).first()


def read_all_rides(
    db: Session, 
    skip: int = 0, 
    limit: int = 10, 
    operator_id: uuid.UUID | None = None,
    ride_status_id: int | None = None,
    glider_name: str | None = None,
    route_id: int | None = None,
    start_time: datetime | None = None,
    end_time: datetime | None = None,
    customer_id: int | None = None,
    use_pagination: bool = True
) -> list[Ride]:
    query = db.query(Ride).order_by(Ride.departure_time.desc())
    
    if operator_id:
        query = query.filter(Ride.operator_id == operator_id)

    if ride_status_id:
        query = query.filter(Ride.ride_status_id == ride_status_id)

    if glider_name:
        query = query.filter(Ride.glider_name == glider_name)

    if route_id:
        query = query.filter(Ride.route_id == route_id)

    if start_time:
        query = query.filter(Ride.departure_time >= start_time)
    if end_time:
        query = query.filter(Ride.departure_time <= end_time)

    if customer_id:
        all_customer_routes = db.query(Route)\
            .filter(Route.customer_id == customer_id)\
            .all()
        route_ids = [route.id for route in all_customer_routes]
        query = query.filter(Ride.route_id.in_(route_ids))
    
    if use_pagination:
        return query.offset(skip).limit(limit).all()
    return query.all()


def read_rides_today(
    db: Session,
    skip: int = 0,
    limit: int = 10,
    operator_id: uuid.UUID | None = None,
    only_pending_and_flying: int = 0,
) -> list[Ride]:
    today = datetime.now().date()
    status_list = (
        [1, 2] if only_pending_and_flying else [1, 2, 3, 4]
    )  # TODO: Change this to a query, not constant values
    if operator_id:
        return (
            db.query(Ride)
            .filter(Ride.operator_id == operator_id)
            .filter(cast(Ride.departure_time, Date) == today)
            .filter(Ride.ride_status_id.in_(status_list))
            .offset(skip)
            .limit(limit)
            .all()
        )
    return (
        db.query(Ride)
        .filter(cast(Ride.departure_time, Date) == today)
        .filter(Ride.ride_status_id.in_(status_list))
        .offset(skip)
        .limit(limit)
        .all()
    )


def read_ride_status_by_id(db: Session, ride_status_id: int) -> RideStatus | None:
    return db.query(RideStatus).filter(RideStatus.id == ride_status_id).first()


def read_all_ride_statuses(db: Session) -> list[RideStatus]:
    return db.query(RideStatus).all()


def read_ride_status_by_name(db: Session, name: str) -> RideStatus | None:
    return db.query(RideStatus).filter(RideStatus.name == name).first()


def read_route_by_id(db: Session, route_id: int) -> Route | None:
    return db.query(Route).filter(Route.id == route_id).first()


def read_all_routes(db: Session, skip: int = 0, limit: int = 10) -> list[Route]:
    return db.query(Route).offset(skip).limit(limit).all()


def read_customer_by_id(db: Session, customer_id: int) -> Customer | None:
    return db.query(Customer).filter(Customer.id == customer_id).first()


def read_all_customers(db: Session, skip: int = 0, limit: int = 10) -> list[Customer]:
    return db.query(Customer).offset(skip).limit(limit).all()


def read_shifts(
    db: Session, 
    start_date: date | None = None,
    end_date: date | None = None,
    pilot_email: str | None = None,
    route_id: int | None = None,
    ride_id: int | None = None,
    customer_id: int | None = None,
    skip: int = 0, 
    limit: int = 10,
    use_pagination: bool = True
) -> list[Shift]:
    query = db.query(Shift).order_by(Shift.start_time.desc())
    
    if start_date:
        query = query.filter(cast(Shift.start_time, Date) >= start_date)
    if end_date:
        query = query.filter(cast(Shift.start_time, Date) <= end_date)
    if pilot_email:
        query = query.filter(Shift.pilot_email == pilot_email)
    if route_id:
        query = query.filter(Shift.route_id == route_id)
    if ride_id:
        query = query.filter(Shift.ride_id == ride_id)

    if customer_id:
        all_customer_routes = db.query(Route)\
            .filter(Route.customer_id == customer_id)\
            .all()
        route_ids = [route.id for route in all_customer_routes]
        query = query.filter(Shift.route_id.in_(route_ids))
    
    if use_pagination:
        return query.offset(skip).limit(limit).all()
    return query.all()


def read_last_shift(db: Session, pilot_email: str) -> Shift | None:
    return db.query(Shift)\
        .filter(Shift.pilot_email == pilot_email)\
        .order_by(Shift.start_time.desc())\
        .first()


def get_shift_table_entry_by_id(
    db: Session,
    shift_id: int
) -> Shift | None:
    return db.query(Shift)\
        .filter(Shift.id == shift_id)\
        .first()


def read_route_id_from_location_ids(
    db: Session,
    start_location_id: int,
    end_location_id: int
) -> Route | None:
    return db.query(Route)\
        .filter(Route.start_location_id == start_location_id)\
        .filter(Route.end_location_id == end_location_id)\
        .first()


def read_all_time_tracking_events(db: Session) -> list[TimeTrackingEvent]:
    return db.query(TimeTrackingEvent).all()

def read_time_tracking_events_by_pilot_email(db: Session, pilot_email: str) -> list[TimeTrackingEvent]:
    return db.query(TimeTrackingEvent).filter(TimeTrackingEvent.pilot_email == pilot_email).all()