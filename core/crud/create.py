from sqlalchemy.orm import Session
from datetime import datetime
from core import Ride, RideStatus
from core import RideIn
from core import Route
from core import RouteIn
from core import Customer
from core import CustomerIn
from core import Shift
from core.crud.time_tracking import create_time_tracking_event
from core.models.tables import TimeTrackingEventType, CancelReason
from core.schemas.schemas import TimeTrackingEventCreate, CancelReasonIn


def create_cancel_reason(db: Session, new_cancel_reason: CancelReasonIn) -> CancelReason:
    cancel_reason = CancelReason(
        name=new_cancel_reason.name,
        description=new_cancel_reason.description
    )
    db.add(cancel_reason)
    db.commit()
    db.refresh(cancel_reason)
    return cancel_reason

def create_new_ride(db: Session, new_ride: RideIn) -> Ride:
    ride = Ride(
        from_location=new_ride.from_location,
        to_location=new_ride.to_location,
        departure_time=new_ride.departure_time,
        arrival_time=new_ride.arrival_time,
        ride_status_id=new_ride.ride_status_id,
        glider_id=new_ride.glider_id,
        glider_name=new_ride.glider_name,
        operator_id=new_ride.operator_id,
        has_package=new_ride.has_package,
        package_description=new_ride.package_description,
        route_id=new_ride.route_id,
        cancel_reason_id=new_ride.cancel_reason_id,
    )
    db.add(ride)
    db.commit()
    db.refresh(ride)
    return ride


def create_new_ride_status(
    db: Session, name: str, description: str | None = None
) -> None:
    new_ride_status = RideStatus(name=name, description=description)
    db.add(new_ride_status)
    db.commit()
    db.refresh(new_ride_status)


def create_new_route(db: Session, new_route: RouteIn) -> Route:
    route = Route(
        start_location_id = new_route.start_location_id,
        start_location_name = new_route.start_location_name,
        end_location_id = new_route.end_location_id,
        end_location_name = new_route.end_location_name,
        emergency_contact = new_route.emergency_contact,
        known_dangers = new_route.known_dangers,
        extra_notes = new_route.extra_notes,
        external_route_id = new_route.external_route_id,
        customer_id = new_route.customer_id
    )
    db.add(route)
    db.commit()
    db.refresh(route)
    return route


def create_new_customer(db: Session, customer: CustomerIn) -> Customer:
    db_customer = Customer(name=customer.name)
    db.add(db_customer)
    db.commit()
    db.refresh(db_customer)
    return db_customer


def create_shift_table_entry_and_start_tracking(
    db: Session,
    pilot_email: str,
    route_id: int,
    start_time: datetime,
    stop_time: datetime | None = None,
    description: str | None = None,
    ride_id: int | None = None
) -> Shift:
    try:
        new_shift = Shift(
            pilot_email=pilot_email,
            route_id=route_id,
            start_time=start_time,
            stop_time=stop_time,
            description=description,
            ride_id=ride_id
        )
        db.add(new_shift)
        db.flush()

        event = TimeTrackingEventCreate(
            route_id=route_id,
            pilot_email=pilot_email,
            event=TimeTrackingEventType.START,
            shift_id=new_shift.id,
            event_timestamp=start_time
        )

        start_event = create_time_tracking_event(db, event, with_commit=False)

        db.commit()
        db.refresh(new_shift)
        db.refresh(start_event)
        return new_shift, start_event
    except Exception as e:
        db.rollback()
        raise e
