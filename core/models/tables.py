from sqlalchemy import Column, Inte<PERSON>, <PERSON>, <PERSON>olean, DateTime, ForeignKey, MetaData, Table, text
from sqlalchemy.orm import relationship, Mapped
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import UUID
from enum import Enum as PyEnum
from datetime import datetime
from core.database.postgres import Base

class CancelReason(Base):
    __tablename__ = "cancel_reason"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    description = Column(String(511), nullable=True)
    ride = relationship("Ride", back_populates="cancel_reason", lazy=True)


class RideStatus(Base):
    __tablename__ = "ride_status"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), unique=True)
    description = Column(String(511), nullable=True)
    ride = relationship("Ride", back_populates="ride_status", lazy=True)

class Route(Base):
    __tablename__ = "route"

    id = Column(Integer, primary_key=True, index=True)
    start_location_id = Column(Integer, nullable=False)
    start_location_name = Column(String(255), nullable=True)
    end_location_id = Column(Integer, nullable=False)
    end_location_name = Column(String(255), nullable=True)
    emergency_contact = Column(String(255), nullable=False)
    known_dangers = Column(String(1023), nullable=True)
    extra_notes = Column(String(1023), nullable=True)
    external_route_id = Column(String(255), nullable=False, unique=True, server_default=text('gen_random_uuid()'))
    customer_id = Column(Integer, ForeignKey("customer.id"), nullable=True)
    customer = relationship("Customer", back_populates="route")
    ride = relationship("Ride", back_populates="route", lazy=True)


class Ride(Base):
    __tablename__ = "ride"

    id = Column(Integer, primary_key=True, index=True)
    from_location = Column(Integer, nullable=True)
    to_location = Column(Integer, nullable=True)
    departure_time = Column(DateTime, nullable=True)
    arrival_time = Column(DateTime, nullable=True)
    ride_status_id = Column(Integer, ForeignKey(RideStatus.id), nullable=True)
    ride_status: Mapped[RideStatus] = relationship(
        "RideStatus", back_populates="ride", lazy=True
    )
    glider_id = Column(Integer, nullable=True)
    glider_name = Column(String(255), nullable=True)
    operator_id = Column(UUID(as_uuid=True), nullable=True)
    has_package = Column(Boolean, default=False)
    package_description = Column(String(511), nullable=True)
    route_id = Column(Integer, ForeignKey(Route.id), nullable=True)
    route: Mapped[Route] = relationship(
        "Route", back_populates="ride", lazy=True
    )
    cancel_reason_id = Column(
        Integer, ForeignKey(CancelReason.id), nullable=True
    )
    cancel_reason: Mapped[CancelReason] = relationship(
        "CancelReason", back_populates="ride", lazy=True
    )


class Customer(Base):
    __tablename__ = "customer"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    route = relationship("Route", back_populates="customer")



class TimeTrackingEventType(str, PyEnum):
    START = "START_TIMER"
    STOP = "STOP_TIMER"
    EDIT = "EDIT_TIMER"


class TimeTrackingEvent(Base):
    __tablename__ = "time_tracking_event"

    id = Column(Integer, primary_key=True, index=True)
    route_id = Column(Integer, ForeignKey("route.id"), nullable=False)
    shift_id = Column(Integer, ForeignKey("shift.id"), nullable=True)
    pilot_email = Column(String(255), nullable=False)
    event = Column(String(64), nullable=False)
    event_timestamp = Column(DateTime, nullable=False)
    created_at = Column(DateTime, server_default=func.now(), nullable=False)

    route = relationship("Route", backref="time_tracking_events")
    shift = relationship("Shift", backref="time_tracking_events")


class Shift(Base):
    __tablename__ = "shift"

    id = Column(Integer, primary_key=True, index=True)
    pilot_email = Column(String(255), nullable=False)
    route_id = Column(Integer, ForeignKey("route.id"), nullable=False)
    start_time = Column(DateTime, nullable=False)
    stop_time = Column(DateTime, nullable=True)
    description = Column(String(255), nullable=True)
    ride_id = Column(Integer, ForeignKey("ride.id"), nullable=True)

    route = relationship("Route", backref="shifts")
    ride = relationship("Ride", backref="shifts")
    created_at = Column(DateTime, server_default=func.now(), nullable=False)
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now(), nullable=False)
