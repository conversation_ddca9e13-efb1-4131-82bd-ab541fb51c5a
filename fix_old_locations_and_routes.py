import requests
import datetime

from sqlalchemy.orm import Session

from core import session_local
from core import Route, Ride, RouteIn
from core import read_route_by_id, create_new_route


is_prod = input("Is this production? (y/n) ")

# Define the Keycloak server URL and client credentials
keycloak_url = "https://auth.uphi.ch/realms/jedsy/protocol/openid-connect/token" if is_prod == "y" else "https://auth.uphi.cc/realms/jedsy/protocol/openid-connect/token"
client_id = "schedule-uploader-local"
client_secret = "Q8hVoc187RnMujC3hOAa64U9TuVVN2Jb" if is_prod == "y" else "FXEjMCJp2eeRR1DRKQNW9dLcqxDyqDYw"

# Create the payload for the token request|
payload = {
    'grant_type': 'client_credentials',
    'client_id': client_id,
    'client_secret': client_secret
}

# Send the POST request to Keycloak to get the token
response = requests.post(keycloak_url, data=payload)

# Check if the request was successful
if response.status_code == 200:
    token_data = response.json()
    access_token = token_data['access_token']
else:
    print("Failed to authenticate:", response.status_code, response.text)
    exit()

headers = {
    'Authorization': 'Bearer ' + access_token,
    'Content-Type': 'application/json'
}



glider_base_url = "https://glider.uphi.ch/api" if is_prod == "y" else "https://glider.uphi.cc/api"

all_locations = requests.get(f"{glider_base_url}/locations", headers=headers).json()
print("Found locations:", len(all_locations))

get_location_id_by_name = {
    x['name']: x['id'] for x in all_locations
}

get_location_name_by_id = {
    x['id']: x['name'] for x in all_locations
}


ride_base_url = "https://ride.uphi.ch" if is_prod == "y" else "https://ride.uphi.cc"

all_routes = requests.get(f"{ride_base_url}/routes?skip=0&limit=10000", headers=headers).json()
print("Found routes:", len(all_routes))

all_rides = []
skip = 0
limit = 2000
while True:
    rides = requests.get(f"{ride_base_url}/rides?skip={skip}&limit={limit}", headers=headers).json()
    # extend all_rides by only the id and route_id of each ride in rides
    rides = [{k: v for k, v in ride.items() if k in ['id', 'route_id', 'from_location', 'to_location']} for ride in rides]
    all_rides.extend(rides)
    if len(rides) < limit:
        break
    skip += limit

print("Found rides:", len(all_rides))


# Add locations Unkown 1 and Unkown 2
response = requests.post(f"{glider_base_url}/locations", headers=headers, json={
    "name": "Unknown 1",
    "pictureUrl": "",
    "videoUrl": "",
    "latitude": 0,
    "longitude": 0,
    "radius": 0,
    "locationCategoryId": 1,
    "locationStatusId": 1,
    "createdAt": datetime.datetime.now().isoformat(),
    "updatedAt": datetime.datetime.now().isoformat()
})
if response.status_code != 200:
    print(f"Failed to add location: Unknown 1")
    print(response.text)
    exit()
else:
    get_location_id_by_name["Unknown 1"] = response.json()["id"]
    get_location_name_by_id[response.json()["id"]] = "Unknown 1"


response = requests.post(f"{glider_base_url}/locations", headers=headers, json={
    "name": "Unknown 2",
    "pictureUrl": "",
    "videoUrl": "",
    "latitude": 0,
    "longitude": 0,
    "radius": 0,
    "locationCategoryId": 1,
    "locationStatusId": 1,
    "createdAt": datetime.datetime.now().isoformat(),
    "updatedAt": datetime.datetime.now().isoformat()
})
if response.status_code != 200:
    print(f"Failed to add location: Unknown 2")
    print(response.text)
    exit()
else:
    get_location_id_by_name["Unknown 2"] = response.json()["id"]
    get_location_name_by_id[response.json()["id"]] = "Unknown 2"

# Add customer Unknown
response = requests.post(f"{ride_base_url}/customers?skip=0&limit=1000", headers=headers, json={
    "name": "Unknown"
})
if response.status_code != 201:
    print(f"Failed to add customer: Unknown")
    print(response.text)
    exit()
else:
    unknown_customer_id = response.json()["id"]

# Add a route between Unknown 1 and Unknown 2
response = requests.post(f"{ride_base_url}/routes", headers=headers, json={
    "start_location_id": get_location_id_by_name["Unknown 1"],
    "start_location_name": "Unknown 1",
    "end_location_id": get_location_id_by_name["Unknown 2"],
    "end_location_name": "Unknown 2",
    "emergency_contact": "",
    "known_dangers": "",
    "extra_notes": "",
    "customer_id": unknown_customer_id
})
if response.status_code != 201:
    print(f"Failed to add route: Unknown 1 -> Unknown 2")
    print(response.text)
    exit()
else:
    all_routes.append(response.json())

unknown_route_id = response.json()["id"]

# Start the for loop only from ride with id 815
starting_point = 0
for i in range(len(all_rides)):
    if all_rides[i]["id"] == 815:
        starting_point = i
        break

for ride in all_rides[starting_point+1:]:
    db: Session = session_local()
    print("Processing ride:", ride["id"])
    try:
       ride['from_location'] = get_location_name_by_id[ride["from_location"]]
    except:
        ride['from_location'] = None

    try:
        ride['to_location'] = get_location_name_by_id[ride["to_location"]]
    except:
        ride['to_location'] = None

    if ride["from_location"] is None:
        ride["from_location"] = "Unknown 1"

    if ride["to_location"] is None:
        ride["to_location"] = "Unknown 2"

    # Check that location names exist in the list of location, else add them to the glider ms
    if ride["from_location"] not in get_location_id_by_name:
        print(f"Adding new location: {ride['from_location']}")
        response = requests.post(f"{glider_base_url}/locations", headers=headers, json={
            "name": ride["from_location"],
            "pictureUrl": "",
            "videoUrl": "",
            "latitude": 0,
            "longitude": 0,
            "radius": 0,
            "locationCategoryId": 1,
            "locationStatusId": 1,
            "createdAt": datetime.datetime.now().isoformat(),
            "updatedAt": datetime.datetime.now().isoformat()
        })
        if response.status_code != 200:
            print(f"Failed to add location: {ride['from_location']}")
            print(response.text)
            exit()
        else:
            get_location_id_by_name[ride["from_location"]] = response.json()["id"]
            get_location_name_by_id[response.json()["id"]] = ride["from_location"]
    
    if ride["to_location"] not in get_location_id_by_name:
        print(f"Adding new location: {ride['to_location']}")
        response = requests.post(f"{glider_base_url}/locations", headers=headers, json={
            "name": ride["to_location"],
            "pictureUrl": "",
            "videoUrl": "",
            "latitude": 0,
            "longitude": 0,
            "radius": 0,
            "locationCategoryId": 1,
            "locationStatusId": 1,
            "createdAt": datetime.datetime.now().isoformat(),
            "updatedAt": datetime.datetime.now().isoformat()
        })
        if response.status_code != 200:
            print(f"Failed to add location: {ride['to_location']}")
            print(response.text)
            exit()
        else:
            get_location_id_by_name[ride["to_location"]] = response.json()["id"]
            get_location_name_by_id[response.json()["id"]] = ride["to_location"]

    if ride["from_location"] == ride["to_location"]:
        print(f"Same location: {ride['from_location']} -> {ride['to_location']}")
        db.query(Ride).filter(Ride.id == ride["id"]).update({"route_id": unknown_route_id, "from_location": get_location_id_by_name["Unknown 1"], "to_location": get_location_id_by_name["Unknown 2"]})
        db.commit()
        db.close()
        continue

    # Check that the route between the two locations exists, else add it to rides ms
    route_id = unknown_route_id
    for route_i in all_routes:
        if route_i["start_location_name"] == ride["from_location"] and route_i["end_location_name"] == ride["to_location"]:
            route_id = route_i["id"]
            break
    route = read_route_by_id(db, route_id)
    route = {
        "id": route.id,
        "start_location_id": route.start_location_id,
        "start_location_name": route.start_location_name,
        "end_location_id": route.end_location_id,
        "end_location_name": route.end_location_name,
        "emergency_contact": route.emergency_contact,
        "known_dangers": route.known_dangers,
        "extra_notes": route.extra_notes,
    }
    if route_id == unknown_route_id:
        print(f"Adding new route: {ride['from_location']} -> {ride['to_location']}")
        new_route = create_new_route(db, RouteIn(
            start_location_id=get_location_id_by_name[ride["from_location"]],
            start_location_name=ride["from_location"],
            end_location_id=get_location_id_by_name[ride["to_location"]],
            end_location_name=ride["to_location"],
            emergency_contact="",
            known_dangers="",
            extra_notes="",
            customer_id=unknown_customer_id
        ))
        new_route_dict = {
            "id": new_route.id,
            "start_location_id": new_route.start_location_id,
            "start_location_name": new_route.start_location_name,
            "end_location_id": new_route.end_location_id,
            "end_location_name": new_route.end_location_name,
            "emergency_contact": new_route.emergency_contact,
            "known_dangers": new_route.known_dangers,
            "extra_notes": new_route.extra_notes,
        }
        route = new_route_dict
        all_routes.append(route)
    else:
        if route["start_location_id"] != get_location_id_by_name[ride["from_location"]]:
            print(f"Updating route: {ride['from_location']} -> {ride['to_location']}")
            # Update using direct db connection
            db.query(Route).filter(Route.id == route_id).update({"start_location_id": get_location_id_by_name[ride["from_location"]]})
            db.commit()
        if route["end_location_id"] != get_location_id_by_name[ride["to_location"]]:
            print(f"Updating route: {ride['from_location']} -> {ride['to_location']}")
            # Update using direct db connection
            db.query(Route).filter(Route.id == route_id).update({"end_location_id": get_location_id_by_name[ride["to_location"]]})
            db.commit()

    # Update the ride with the route id
    print(f"Updating ride: {ride['id']}")
    # Update using direct db connection
    db.query(Ride).filter(Ride.id == ride["id"]).update({"route_id": route_id, "from_location": route["start_location_id"], "to_location": route["end_location_id"]})
    db.commit()
    db.close()

