#!/usr/bin/env python3
"""
Test script to verify admin role checking functionality
"""

from dependencies.dependencies import check_admin_role, KEYCLOAK_CLIENT_ID

def test_admin_role_checking():
    """Test the admin role checking function with different token structures"""
    
    print(f"Testing admin role checking with client ID: {KEYCLOAK_CLIENT_ID}")
    print("=" * 50)
    
    # Test case 1: Token with realm-level admin role
    token_with_realm_admin = {
        "sub": "user123",
        "realm_access": {
            "roles": ["admin", "user"]
        }
    }
    
    result1 = check_admin_role(token_with_realm_admin)
    print(f"Test 1 - Realm admin role: {result1} (Expected: True)")
    
    # Test case 2: Token with client-level admin role
    token_with_client_admin = {
        "sub": "user123",
        "resource_access": {
            KEYCLOAK_CLIENT_ID: {
                "roles": ["admin", "user"]
            }
        }
    }
    
    result2 = check_admin_role(token_with_client_admin)
    print(f"Test 2 - Client admin role: {result2} (Expected: True)")
    
    # Test case 3: Token with no admin role
    token_without_admin = {
        "sub": "user123",
        "realm_access": {
            "roles": ["user", "viewer"]
        },
        "resource_access": {
            KEYCLOAK_CLIENT_ID: {
                "roles": ["user", "viewer"]
            }
        }
    }
    
    result3 = check_admin_role(token_without_admin)
    print(f"Test 3 - No admin role: {result3} (Expected: False)")
    
    # Test case 4: Empty token
    empty_token = {}
    
    result4 = check_admin_role(empty_token)
    print(f"Test 4 - Empty token: {result4} (Expected: False)")
    
    # Test case 5: Token with both realm and client admin roles
    token_with_both_admin = {
        "sub": "user123",
        "realm_access": {
            "roles": ["admin", "user"]
        },
        "resource_access": {
            KEYCLOAK_CLIENT_ID: {
                "roles": ["admin", "user"]
            }
        }
    }
    
    result5 = check_admin_role(token_with_both_admin)
    print(f"Test 5 - Both admin roles: {result5} (Expected: True)")
    
    print("=" * 50)
    print("Admin role checking tests completed!")
    
    # Summary
    all_tests_passed = all([
        result1 == True,
        result2 == True,
        result3 == False,
        result4 == False,
        result5 == True
    ])
    
    print(f"All tests passed: {all_tests_passed}")
    return all_tests_passed

if __name__ == "__main__":
    test_admin_role_checking()
