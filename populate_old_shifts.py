from core import read_all_time_tracking_events, read_time_tracking_events_by_pilot_email
from core import Shift, TimeTrackingEvent
from core import session_local
from sqlalchemy.orm import Session


# Get all unique pilot emails from time tracking events
def get_unique_pilot_emails(db: Session) -> set[str]:
    events = read_all_time_tracking_events(db)
    return {event.pilot_email for event in events if event.pilot_email}

# For each pilot email, get the time tracking events, sorted by id
def get_sorted_time_tracking_events(db: Session, pilot_email: str) -> list[TimeTrackingEvent]:
    events = read_time_tracking_events_by_pilot_email(db, pilot_email)
    return sorted(events, key=lambda event: event.id)

# and create shifts based on the events (update shift_id in time tracking events as well)
# We assume that START_TIMER and STOP_TIMER events are used to define shifts
# And that events are consecutive, meaning START_TIMER is always followed by STOP_TIMER
def create_shifts_for_all_pilots(db: Session) -> None:
    unique_pilots = get_unique_pilot_emails(db)
    
    for pilot_email in unique_pilots:
        events = get_sorted_time_tracking_events(db, pilot_email)
        print(f"Processing events for pilot: {pilot_email}, Total events: {len(events)}")
        print("Checking consistency of events...")
        flag = check_consistency(events)
        if not flag:
            print(f"Inconsistent events found for pilot {pilot_email}. Skipping shift creation.")
            print()
            continue
        create_shifts_from_events(db, pilot_email, events)

def check_consistency(events: list[TimeTrackingEvent]) -> bool:
    shift_start = None
    for event in events:
        if event.event == "START_TIMER":
            if shift_start is not None:
                print(f"Error: Multiple START_TIMER without STOP_TIMER for event {event.id}")
                return False
            shift_start = event.event_timestamp
        elif event.event == "STOP_TIMER":
            if shift_start is None:
                print(f"Error: STOP_TIMER without START_TIMER for event {event.id}")
                return False
            shift_start = None  # Reset after a valid stop
    if shift_start is not None:
        print("Warning: Unmatched START_TIMER at the end of events.")
    return True

def create_shifts_from_events(db: Session, pilot_email: str, events: list[TimeTrackingEvent]) -> None:
    shift_start = None
    shift_end = None
    shift_description = None

    for event in events:
        print(f"Processing event {event.event} for pilot {pilot_email} at {event.event_timestamp}")
        print("Event Type:", event.event)
        if event.event == "START_TIMER":
            if shift_start is not None:
                # If we already have a start time, this is an error in the data
                print(f"Error: START_TIMER without STOP_TIMER for {pilot_email} at {event.event_timestamp}")
                continue
            shift_start = event.event_timestamp
            shift_description = f"Shift started at {shift_start}"
        
        elif event.event == "STOP_TIMER":
            if shift_start is None:
                # If we have a STOP_TIMER without a START_TIMER, this is an error in the data
                print(f"Error: STOP_TIMER without START_TIMER for {pilot_email} at {event.event_timestamp}")
                continue
            shift_end = event.event_timestamp
            
            # Create the Shift object and add it to the session
            new_shift = Shift(
                pilot_email=pilot_email,
                route_id=event.route_id,
                start_time=shift_start,
                stop_time=shift_end,
                description=shift_description
            )
            db.add(new_shift)
            
            # Update the time tracking event with the new shift ID
            event.shift_id = new_shift.id
            
            # Reset for the next shift
            shift_start = None
            shift_end = None
            shift_description = None
            print()

    # Commit all changes to the database
    db.commit()

if __name__ == "__main__":
    db : Session = session_local()
    try:
        create_shifts_for_all_pilots(db)
        print("Shifts populated successfully.")
    except Exception as e:
        print(f"An error occurred: {e}")
        db.rollback()
    finally:
        db.close()