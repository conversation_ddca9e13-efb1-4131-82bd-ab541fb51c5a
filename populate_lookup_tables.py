from sqlalchemy.orm import Session
from core import session_local
from core import read_ride_status_by_name, create_new_ride_status


class DuplicateEnteryError(Exception):
    def __init__(self, name: str):
        self.name = name


def add_ride_status(
    name: str,
    description: str | None = None,
    db: Session = session_local(),
):
    try:
        if exists_ride_status(db, name):
            raise DuplicateEnteryError(name)
        create_new_ride_status(db=db, name=name, description=description)
    except DuplicateEnteryError as e:
        print(f"The ride status with name '{name}' already exists.")
    except Exception as e:
        db.rollback()
        print(f"An error occured while adding ride status '{name}'. Aborting...")
    finally:
        db.close()


def exists_ride_status(db: Session, name: str):
    matching_ride_status = read_ride_status_by_name(db, name)
    if matching_ride_status is None:
        return False
    return True


add_ride_status("Pending", "The ride is pending.")
add_ride_status("Completed", "The ride is complete.")
add_ride_status("Cancelled", "The ride was cancelled.")


print("Lookup tables are populated successfully!")
