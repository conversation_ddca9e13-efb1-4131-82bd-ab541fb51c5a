from sqlalchemy.orm import Session
from core import RideStatus


# Check that the ride status is valid:
# The ride status id must be in the range of valid ride status ids
# We query the database tabe ride_status to get all the valid ride status ids
def is_valid_ride_status(db: Session, ride_status_id: int) -> bool:
    return (
        db.query(RideStatus).filter(RideStatus.id == ride_status_id).first() is not None
    )
