import requests

# Define the Keycloak server URL and client credentials
keycloak_url = "https://auth.uphi.ch/realms/jedsy/protocol/openid-connect/token"
client_id = "ms-glider-backend"
client_secret = "wHMMoHvdpApo66W6b42ynBWB9cVJuw2X"

# Create the payload for the token request|
payload = {
    'grant_type': 'client_credentials',
    'client_id': client_id,
    'client_secret': client_secret
}

# Send the POST request to Keycloak to get the token
response = requests.post(keycloak_url, data=payload)

# Check if the request was successful
if response.status_code == 200:
    token_data = response.json()
    access_token = token_data['access_token']
else:
    print("Failed to authenticate:", response.status_code, response.text)
    exit()

headers = {
    'Authorization': 'Bearer ' + access_token,
    'Content-Type': 'application/json'
}