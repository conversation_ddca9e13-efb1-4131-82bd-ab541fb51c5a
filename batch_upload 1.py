import requests
import pandas as pd
from datetime import timedelta, datetime, date, time
import argparse
import traceback

parser = argparse.ArgumentParser(description='Upload schedule to the server')

parser.add_argument('file', type=str, help='The file to upload')
parser.add_argument('sheet', type=str, help='The sheet to upload')

args = parser.parse_args()

FILE_NAME = args.file
SHEET_NAME = args.sheet


# Define the Keycloak server URL and client credentials
keycloak_url = "https://auth.uphi.ch/realms/jedsy/protocol/openid-connect/token"
client_id = "schedule-uploader-local"
client_secret = "Q8hVoc187RnMujC3hOAa64U9TuVVN2Jb"

# Create the payload for the token request|
payload = {
    'grant_type': 'client_credentials',
    'client_id': client_id,
    'client_secret': client_secret
}

# Send the POST request to Keycloak to get the token
response = requests.post(keycloak_url, data=payload)

# Check if the request was successful
if response.status_code == 200:
    token_data = response.json()
    access_token = token_data['access_token']
else:
    print("Failed to authenticate:", response.status_code, response.text)
    exit()

headers = {
    'Authorization': 'Bearer ' + access_token,
    'Content-Type': 'application/json'
}



gliders_url = "https://glider.uphi.ch/api/gliders"

response = requests.get(gliders_url, headers=headers)

glider_ids = {
    x['name']: x['id'] for x in response.json()
}


users_url = "https://auth.uphi.ch/admin/realms/jedsy/users"

response = requests.get(users_url, headers=headers)
user_ids = {
    x['username']: x['id'] for x in response.json()
}


locations_url = "https://glider.uphi.ch/api/locations"

response = requests.get(locations_url, headers=headers)
location_ids = {
    x['name']: x['id'] for x in response.json()
}



ride_statuses_url = "https://ride.uphi.ch/ride-statuses"

response = requests.get(ride_statuses_url, headers=headers)

ride_status_ids = {
    x['name']: x['id'] for x in response.json()
}


df = pd.read_excel(FILE_NAME, sheet_name=SHEET_NAME)

skipped = 0
added = 0
cnt = 0
for _, row in df.iterrows():
    try:
        try:
            row_date = datetime.strptime(str(row["Date"]), "%m/%d/%Y")
            arrival_time = datetime.strptime(str(row["Arrival Time"]), "%H:%M:%S").time()
            takeoff_time = datetime.strptime(str(row["Takeoff Time"]), "%H:%M:%S").time()
            print("Date:", row_date, "Takeoff Time:", takeoff_time, "Arrival Time:", arrival_time)
            arrival_datetime = (row_date + timedelta(hours=arrival_time.hour, minutes=arrival_time.minute, seconds=arrival_time.second)).isoformat()
            takeoff_datetime = (row_date + timedelta(hours=takeoff_time.hour, minutes=takeoff_time.minute, seconds=takeoff_time.second)).isoformat()
            print("Arrival Datetime:", arrival_datetime, "Takeoff Datetime:", takeoff_datetime)
        except:
            traceback.print_exc()
            print("Invalid date format in row:", row["Date"])
            row_date = datetime.combine(datetime.today(), datetime.min.time())
            arrival_datetime = row_date.isoformat()
            takeoff_datetime = row_date.isoformat()
        data = {
            "from_location": location_ids[row['Start Location']],
            "to_location": location_ids[row['Land Location']],
            "departure_time": takeoff_datetime,
            "arrival_time": arrival_datetime,
            "ride_status_id": ride_status_ids['Pending'],
            "glider_id": glider_ids[row['Drone']],
            "glider_name": row['Drone'],
            "operator_id": user_ids[row['Operator']],
            "has_package": False,
            "package_description": "",
            "route_id": None,
            "cancel_reason": None
        }
        print(data)
        response = requests.post("https://ride.uphi.ch/rides", json=data, headers=headers)
        added += 1
    except Exception as e:
        traceback.print_exc()
        print("Entry skipped : ", e)    
        skipped += 1

print("Added:", added)
print("Skipped:", skipped)

