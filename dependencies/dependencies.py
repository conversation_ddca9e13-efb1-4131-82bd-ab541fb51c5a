from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>earer

from core import session_local
from util import keycloak_openid
from config import Config

conf = Config()


def get_db():
    db = session_local()
    try:
        yield db
    finally:
        db.close()


# Keycloak configuration
KEYCLOAK_BASE_URL = conf.get_ms_ride_auth_base_url()
KEYCLOAK_REALM = conf.get_ms_ride_auth_realm()
KEYCLOAK_CLIENT_ID = conf.get_ms_ride_auth_client_id()
KEYCLOAK_CLIENT_SECRET = conf.get_ms_ride_auth_client_secret()

oauth_scheme = OAuth2PasswordBearer(
    tokenUrl=f"{KEYCLOAK_BASE_URL}/realms/{KEYCLOAK_REALM}/protocol/openid-connect/token"
)

def decode_token(token: str):
    try:
        # Decode and verify the token using KeycloakOpenID
        token_info = keycloak_openid.decode_token(token)
        return token_info
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token",
            headers={"WWW-Authenticate": "Bearer"},
        ) from e


async def get_current_user(token: str = Depends(oauth_scheme)):
    token_info = decode_token(token)
    return token_info


def check_admin_role(token_info: dict) -> bool:
    """
    Check if the user has admin role in the token.
    Keycloak tokens typically contain roles in different places:
    - realm_access.roles for realm-level roles
    - resource_access.{client_id}.roles for client-level roles
    """
    # Check realm-level roles
    realm_access = token_info.get("realm_access", {})
    realm_roles = realm_access.get("roles", [])
    if "admin" in realm_roles:
        return True

    # Check client-level roles
    resource_access = token_info.get("resource_access", {})
    client_access = resource_access.get(KEYCLOAK_CLIENT_ID, {})
    client_roles = client_access.get("roles", [])
    if "admin" in client_roles:
        return True

    return False


async def get_admin_user(token: str = Depends(oauth_scheme)):
    """
    Dependency that ensures the user has admin role.
    Returns the token info if user is admin, otherwise raises 403 Forbidden.
    """
    token_info = decode_token(token)

    if not check_admin_role(token_info):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin role required for this operation",
        )

    return token_info