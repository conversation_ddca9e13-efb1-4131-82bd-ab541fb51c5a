from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>earer

from core import session_local
from util import keycloak_openid
from config import Config

conf = Config()


def get_db():
    db = session_local()
    try:
        yield db
    finally:
        db.close()


# Keycloak configuration
KEYCLOAK_BASE_URL = conf.get_ms_ride_auth_base_url()
KEYCLOAK_REALM = conf.get_ms_ride_auth_realm()
KEYCLOAK_CLIENT_ID = conf.get_ms_ride_auth_client_id()
KEYCLOAK_CLIENT_SECRET = conf.get_ms_ride_auth_client_secret()

oauth_scheme = OAuth2PasswordBearer(
    tokenUrl=f"{KEYCLOAK_BASE_URL}/realms/{KEYCLOAK_REALM}/protocol/openid-connect/token"
)

def decode_token(token: str):
    try:
        # Decode and verify the token using KeycloakOpenID
        token_info = keycloak_openid.decode_token(token)
        return token_info
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token",
            headers={"WWW-Authenticate": "Bearer"},
        ) from e


async def get_current_user(token: str = Depends(oauth_scheme)):
    token_info = decode_token(token)
    return token_info