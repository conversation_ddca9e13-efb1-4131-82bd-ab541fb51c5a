"""add time tracking table

Revision ID: d45fd78c0347
Revises: bc649c2b9610
Create Date: 2025-02-28 14:06:09.548208

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd45fd78c0347'
down_revision: Union[str, None] = 'bc649c2b9610'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('time_tracking_event',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('route_id', sa.Integer(), nullable=False),
    sa.Column('pilot_email', sa.String(length=255), nullable=False),
    sa.Column('event', sa.String(length=64), nullable=False),
    sa.Column('event_timestamp', sa.DateTime(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['route_id'], ['route.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_time_tracking_event_id'), 'time_tracking_event', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_time_tracking_event_id'), table_name='time_tracking_event')
    op.drop_table('time_tracking_event')
    # ### end Alembic commands ###
