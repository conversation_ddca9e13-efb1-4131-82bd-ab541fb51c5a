"""empty message

Revision ID: 51ba945b9103
Revises: 3d6988c08f97
Create Date: 2024-07-31 15:14:06.398618

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '51ba945b9103'
down_revision: Union[str, None] = '3d6988c08f97'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('ride', sa.Column('glider_name', sa.String(length=255), nullable=False))
    op.alter_column('ride', 'glider_id',
               existing_type=sa.INTEGER(),
               nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('ride', 'glider_id',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.drop_column('ride', 'glider_name')
    # ### end Alembic commands ###
