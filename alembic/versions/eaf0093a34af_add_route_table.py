"""add route table

Revision ID: eaf0093a34af
Revises: adf0f7d16dd8
Create Date: 2025-02-28 12:08:58.433033

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'eaf0093a34af'
down_revision: Union[str, None] = 'adf0f7d16dd8'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('route',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('start_location_id', sa.Integer(), nullable=False),
    sa.Column('start_location_name', sa.String(length=255), nullable=True),
    sa.Column('end_location_id', sa.Integer(), nullable=False),
    sa.Column('end_location_name', sa.String(length=255), nullable=True),
    sa.Column('emergency_contact', sa.String(length=255), nullable=False),
    sa.Column('known_dangers', sa.String(length=1023), nullable=True),
    sa.Column('extra_notes', sa.String(length=1023), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_route_id'), 'route', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_route_id'), table_name='route')
    op.drop_table('route')
    # ### end Alembic commands ###
