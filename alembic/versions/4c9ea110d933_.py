"""empty message

Revision ID: 4c9ea110d933
Revises: e29d8269bc33
Create Date: 2025-05-21 16:48:39.634694

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4c9ea110d933'
down_revision: Union[str, None] = 'e29d8269bc33'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('time_tracking_event', sa.Column('shift_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'time_tracking_event', 'shift', ['shift_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'time_tracking_event', type_='foreignkey')
    op.drop_column('time_tracking_event', 'shift_id')
    # ### end Alembic commands ###
