"""empty message

Revision ID: dd0a1b006e04
Revises: 4c9ea110d933
Create Date: 2025-05-21 17:01:19.364316

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'dd0a1b006e04'
down_revision: Union[str, None] = '4c9ea110d933'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('cancel_reason',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.String(length=511), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_cancel_reason_id'), 'cancel_reason', ['id'], unique=False)
    op.add_column('ride', sa.Column('cancel_reason_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'ride', 'cancel_reason', ['cancel_reason_id'], ['id'])
    op.drop_column('ride', 'cancel_reason')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('ride', sa.Column('cancel_reason', sa.VARCHAR(length=511), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'ride', type_='foreignkey')
    op.drop_column('ride', 'cancel_reason_id')
    op.drop_index(op.f('ix_cancel_reason_id'), table_name='cancel_reason')
    op.drop_table('cancel_reason')
    # ### end Alembic commands ###
