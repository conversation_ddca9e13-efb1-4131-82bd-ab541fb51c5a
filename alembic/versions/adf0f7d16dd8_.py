"""empty message

Revision ID: adf0f7d16dd8
Revises: 51ba945b9103
Create Date: 2024-08-20 09:44:18.709138

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'adf0f7d16dd8'
down_revision: Union[str, None] = '51ba945b9103'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('ride', 'from_location',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.alter_column('ride', 'to_location',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.alter_column('ride', 'departure_time',
               existing_type=postgresql.TIMESTAMP(),
               nullable=True)
    op.alter_column('ride', 'arrival_time',
               existing_type=postgresql.TIMESTAMP(),
               nullable=True)
    op.alter_column('ride', 'ride_status_id',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.alter_column('ride', 'glider_name',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
    op.alter_column('ride', 'operator_id',
               existing_type=sa.UUID(),
               nullable=True)
    op.alter_column('ride', 'has_package',
               existing_type=sa.BOOLEAN(),
               nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('ride', 'has_package',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    op.alter_column('ride', 'operator_id',
               existing_type=sa.UUID(),
               nullable=False)
    op.alter_column('ride', 'glider_name',
               existing_type=sa.VARCHAR(length=255),
               nullable=False)
    op.alter_column('ride', 'ride_status_id',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.alter_column('ride', 'arrival_time',
               existing_type=postgresql.TIMESTAMP(),
               nullable=False)
    op.alter_column('ride', 'departure_time',
               existing_type=postgresql.TIMESTAMP(),
               nullable=False)
    op.alter_column('ride', 'to_location',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.alter_column('ride', 'from_location',
               existing_type=sa.INTEGER(),
               nullable=False)
    # ### end Alembic commands ###
