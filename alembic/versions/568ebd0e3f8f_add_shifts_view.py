"""add shifts view

Revision ID: 568ebd0e3f8f
Revises: d45fd78c0347
Create Date: 2025-02-28 14:55:10.510366

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from alembic_utils.pg_view import PGView
from sqlalchemy import text as sql_text

# revision identifiers, used by Alembic.
revision: str = '568ebd0e3f8f'
down_revision: Union[str, None] = 'd45fd78c0347'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    public_shifts = PGView(
        schema="public",
        signature="shifts",
        definition="WITH ranked_events AS (\n            SELECT \n                pilot_email,\n                route_id,\n                event,\n                event_timestamp,\n                LEAD(event_timestamp) OVER (\n                    PARTITION BY pilot_email, route_id \n                    ORDER BY event_timestamp\n                ) as next_event_time,\n                LEAD(event) OVER (\n                    PARTITION BY pilot_email, route_id \n                    ORDER BY event_timestamp\n                ) as next_event\n            FROM time_tracking_event\n        )\n        SELECT \n            pilot_email,\n            route_id,\n            event_timestamp as start_time,\n            CASE \n                WHEN next_event = 'STOP_TIMER' THEN next_event_time\n                ELSE NULL\n            END as stop_time\n        FROM ranked_events\n        WHERE event = 'START_TIMER'"
    )
    op.create_entity(public_shifts)

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    public_shifts = PGView(
        schema="public",
        signature="shifts",
        definition="WITH ranked_events AS (\n            SELECT \n                pilot_email,\n                route_id,\n                event,\n                event_timestamp,\n                LEAD(event_timestamp) OVER (\n                    PARTITION BY pilot_email, route_id \n                    ORDER BY event_timestamp\n                ) as next_event_time,\n                LEAD(event) OVER (\n                    PARTITION BY pilot_email, route_id \n                    ORDER BY event_timestamp\n                ) as next_event\n            FROM time_tracking_event\n        )\n        SELECT \n            pilot_email,\n            route_id,\n            event_timestamp as start_time,\n            CASE \n                WHEN next_event = 'STOP_TIMER' THEN next_event_time\n                ELSE NULL\n            END as stop_time\n        FROM ranked_events\n        WHERE event = 'START_TIMER'"
    )
    op.drop_entity(public_shifts)

    # ### end Alembic commands ###
