"""add customer table and mapping

Revision ID: bc649c2b9610
Revises: 6571704b0ba1
Create Date: 2025-02-28 12:53:30.582438

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'bc649c2b9610'
down_revision: Union[str, None] = '6571704b0ba1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('customer',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_customer_id'), 'customer', ['id'], unique=False)
    op.add_column('route', sa.Column('customer_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'route', 'customer', ['customer_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'route', type_='foreignkey')
    op.drop_column('route', 'customer_id')
    op.drop_index(op.f('ix_customer_id'), table_name='customer')
    op.drop_table('customer')
    # ### end Alembic commands ###
