"""add route_id to rides

Revision ID: 6571704b0ba1
Revises: eaf0093a34af
Create Date: 2025-02-28 12:46:51.515175

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '6571704b0ba1'
down_revision: Union[str, None] = 'eaf0093a34af'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('ride', sa.Column('route_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'ride', 'route', ['route_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'ride', type_='foreignkey')
    op.drop_column('ride', 'route_id')
    # ### end Alembic commands ###
