"""empty message

Revision ID: 57b874b90573
Revises: 568ebd0e3f8f
Create Date: 2025-03-06 08:58:17.061584

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '57b874b90573'
down_revision: Union[str, None] = '568ebd0e3f8f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('ride', sa.Column('cancel_reason', sa.String(length=511), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('ride', 'cancel_reason')
    # ### end Alembic commands ###
