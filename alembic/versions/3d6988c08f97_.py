"""empty message

Revision ID: 3d6988c08f97
Revises: 
Create Date: 2024-07-30 16:33:14.521841

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3d6988c08f97'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('ride_status',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=True),
    sa.Column('description', sa.String(length=511), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_ride_status_id'), 'ride_status', ['id'], unique=False)
    op.create_table('ride',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('from_location', sa.Integer(), nullable=False),
    sa.Column('to_location', sa.Integer(), nullable=False),
    sa.Column('departure_time', sa.DateTime(), nullable=False),
    sa.Column('arrival_time', sa.DateTime(), nullable=False),
    sa.Column('ride_status_id', sa.Integer(), nullable=False),
    sa.Column('glider_id', sa.Integer(), nullable=False),
    sa.Column('operator_id', sa.UUID(), nullable=False),
    sa.Column('has_package', sa.Boolean(), nullable=False),
    sa.Column('package_description', sa.String(length=511), nullable=True),
    sa.ForeignKeyConstraint(['ride_status_id'], ['ride_status.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_ride_id'), 'ride', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_ride_id'), table_name='ride')
    op.drop_table('ride')
    op.drop_index(op.f('ix_ride_status_id'), table_name='ride_status')
    op.drop_table('ride_status')
    # ### end Alembic commands ###
