"""empty message

Revision ID: 4d6bb1598659
Revises: dd0a1b006e04
Create Date: 2025-05-22 17:26:46.288413

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4d6bb1598659'
down_revision: Union[str, None] = 'dd0a1b006e04'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('route', sa.Column('external_route_id', sa.String(length=255), server_default=sa.text('gen_random_uuid()'), nullable=False))
    op.create_unique_constraint(None, 'route', ['external_route_id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'route', type_='unique')
    op.drop_column('route', 'external_route_id')
    # ### end Alembic commands ###
