from core import session_local
from core import Route, Ride
from core import read_all_routes, read_all_rides
from sqlalchemy.orm import Session

# We first get the list of all routes
def get_all_routes(db: Session):
    print("Routes found:")
    routes = []
    for route in read_all_routes(db, skip=0, limit=10000):
        routes.append(route)
        print(f"Route ID: {route.id}, Start: {route.start_location_id}, End: {route.end_location_id}")
    return routes

# Then we fetch the length of the Rides table
def get_all_rides(db: Session):
    ride_count = read_all_rides(db, skip=0, limit=10000, use_pagination=False)
    print(f"Total rides found: {len(ride_count)}")
    rides = []
    for ride in ride_count:
        rides.append(ride)
    return rides

# Then, for each ride, we check if the route_id is None
# If it is None, we assign the route_id from the route table based on the location IDs
def populate_old_route_ids(db: Session):
    routes: list[Route] = get_all_routes(db)
    rides: list[Ride] = get_all_rides(db)

    for ride in rides:
        if ride.route_id is None:
            # Find the route that matches the start and end location IDs
            for route in routes:
                if (route.start_location_id == ride.from_location and
                        route.end_location_id == ride.to_location):
                    print(f"Updating Ride ID: {ride.id} with Route ID: {route.id}")
                    ride.route_id = route.id
                    break
            else:
                print(f"No matching route found for Ride ID: {ride.id}")
    db.commit()

if __name__ == "__main__":
    db: Session = session_local()
    try:
        populate_old_route_ids(db)
    finally:
        db.close()