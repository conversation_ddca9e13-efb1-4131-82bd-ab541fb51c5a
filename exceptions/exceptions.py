from fastapi import Request
from fastapi.responses import JSONResponse


class SameLocationException(Exception):
    def __init__(self, location_id: int):
        self.location_id = location_id


async def same_location_exception_handler(request: Request, e: SameLocationException):
    return JSONResponse(
        status_code=409,
        content=f"Location with id {e.location_id} is duplicated for starting and ending locations!",
    )


class InvalidTimeException(Exception):
    def __init__(self):
        pass


async def invalid_time_exception_handler(request: Request, e: InvalidTimeException):
    return JSONResponse(
        status_code=409,
        content="Departure time must be before arrival time, and both must have valid datetime format!",
    )


class InvalidRideStatusException(Exception):
    def __init__(self):
        pass


async def invalid_ride_status_exception_handler(
    request: Request, e: InvalidRideStatusException
):
    return JSONResponse(
        status_code=404,
        content="Ride status is not found!",
    )


class MissingPackageDescriptionException(Exception):
    def __init__(self):
        pass


async def missing_package_description_exception_handler(
    request: Request, e: MissingPackageDescriptionException
):
    return JSONResponse(
        status_code=409,
        content="Package description is required when has_package is True!",
    )


class UndefinedException(Exception):
    def __init__(self):
        pass


async def undefined_exception_handler(request: Request, e: UndefinedException):
    return JSONResponse(
        status_code=500,
        content="An undefined error occurred!",
    )


class RideNotFoundException(Exception):
    def __init__(self, ride_id: int):
        self.ride_id = ride_id


async def ride_not_found_exception_handler(request: Request, e: RideNotFoundException):
    return JSONResponse(
        status_code=404,
        content=f"Ride with id {e.ride_id} is not found!",
    )


class RideStatusNotFoundException(Exception):
    def __init__(self, ride_status_id: int):
        self.ride_status_id = ride_status_id


async def ride_status_not_found_exception_handler(
    request: Request, e: RideStatusNotFoundException
):
    return JSONResponse(
        status_code=404,
        content=f"Ride status with id {e.ride_status_id} is not found!",
    )


class RouteNotFoundException(Exception):
    def __init__(self, route_id: int):
        self.route_id = route_id


async def route_not_found_exception_handler(request: Request, e: RouteNotFoundException):
    return JSONResponse(
        status_code=404,
        content=f"Route with id {e.route_id} is not found!",
    )


class CustomerNotFoundException(Exception):
    def __init__(self, customer_id: int):
        self.customer_id = customer_id


async def customer_not_found_exception_handler(request: Request, e: CustomerNotFoundException):
    return JSONResponse(
        status_code=404,
        content=f"Customer with id {e.customer_id} is not found!"
    )


class InvalidTimeTrackingException(Exception):
    def __init__(self, message: str):
        self.message = message


async def invalid_time_tracking_exception_handler(request: Request, e: InvalidTimeTrackingException):
    return JSONResponse(
        status_code=400,
        content=e.message
    )
