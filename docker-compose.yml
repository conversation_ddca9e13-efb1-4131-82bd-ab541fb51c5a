version: '3.8'

services:
  backend:
    build: 
      context: .
      dockerfile: Dockerfile
    command: /bin/sh ./run_server.sh
    depends_on:
      - db-postgres
    volumes:
      - .:/app
    ports:
      - "8000:8000"
    environment:
      - POSTGRES_URL = ${POSTGRES_URL}
  db-postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ms-ride