from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from core import RideStatusResponse
from dependencies import get_db
from core import read_ride_status_by_id, read_all_ride_statuses
from exceptions import *
from util import *


ride_status_router = APIRouter()


@ride_status_router.get(
    "/ride-statuses/{ride_status_id}",
    tags=["Ride Statuses"],
    status_code=200,
    response_model=RideStatusResponse,
)
async def get_ride_status(ride_status_id: int, db: Session = Depends(get_db)):
    ride_status = read_ride_status_by_id(db, ride_status_id)
    if ride_status is None:
        raise RideStatusNotFoundException(ride_status_id=ride_status_id)

    return ride_status


@ride_status_router.get(
    "/ride-statuses",
    tags=["Ride Statuses"],
    status_code=200,
    response_model=list[RideStatusResponse],
)
async def get_ride_statuses(db: Session = Depends(get_db)):
    return read_all_ride_statuses(db)
