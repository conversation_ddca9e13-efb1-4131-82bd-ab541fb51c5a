from fastapi import APIRouter, Depends, Query, HTTPException
from sqlalchemy.orm import Session
from datetime import date

from core.schemas.schemas import ShiftOut
from core.crud.read import read_shifts, read_last_shift
from dependencies import get_db

from exceptions import InvalidTimeException

shift_router = APIRouter()

@shift_router.get("/shifts", tags=["Shifts"], response_model=list[ShiftOut])
async def get_shifts(
    start_date: str | None = Query(None, description="Filter shifts starting from this date"),
    end_date: str | None = Query(None, description="Filter shifts until this date"),
    pilot_email: str | None = Query(None, description="Filter shifts by pilot email"),
    route_id: int | None = Query(None, description="Filter shifts by route ID"),
    ride_id: int | None = Query(None, description="Filter shifts by ride ID"),
    customer_id: int | None = Query(None, description="Filter shifts by customer ID"),
    skip: int = Query(0, description="Number of records to skip"),
    limit: int = Query(10, description="Number of records to return"),
    db: Session = Depends(get_db)
):
    if start_date:
        try:
            start_date = date.fromisoformat(start_date)
        except ValueError:
            raise InvalidTimeException()
    if end_date:
        try:
            end_date = date.fromisoformat(end_date)
        except ValueError:
            raise InvalidTimeException()
    return read_shifts(
        db, 
        start_date=start_date,
        end_date=end_date,
        pilot_email=pilot_email,
        route_id=route_id,
        ride_id=ride_id,
        customer_id=customer_id,
        skip=skip,
        limit=limit
    )

@shift_router.get("/shifts/last", tags=["Shifts"], response_model=ShiftOut)
async def get_last_shift(
    pilot_email: str = Query(..., description="Pilot email to get the last shift for"),
    db: Session = Depends(get_db)
):
    shift = read_last_shift(db, pilot_email)
    if not shift:
        raise HTTPException(
            status_code=404,
            detail=f"No shifts found for pilot {pilot_email}"
        )
    return shift 