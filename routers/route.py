from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from core import RouteIn, RouteOut, RouteUpdate
from dependencies import get_db
from core import create_new_route, read_route_by_id, read_all_routes
from exceptions import *

route_router = APIRouter()

@route_router.post("/routes", tags=["Routes"], status_code=201, response_model=RouteOut)
async def add_route(new_route: RouteIn, db: Session = Depends(get_db)):
    if new_route.start_location_id == new_route.end_location_id:
        raise SameLocationException(new_route.start_location_id)
    
    try:
        new_route = create_new_route(db, new_route)
        return new_route
    except Exception as e:
        print(e)
        raise UndefinedException()

@route_router.get("/routes", tags=["Routes"], status_code=200, response_model=list[RouteOut])
async def get_routes(skip: int = 0, limit: int = 10, db: Session = Depends(get_db)):
    return read_all_routes(db, skip=skip, limit=limit)

@route_router.get("/routes/{route_id}", tags=["Routes"], status_code=200, response_model=RouteOut)
async def get_route(route_id: int, db: Session = Depends(get_db)):
    route = read_route_by_id(db, route_id)
    if route is None:
        raise RouteNotFoundException(route_id=route_id)
    return route 