from fastapi import APIRouter, Depends
from fastapi.responses import StreamingResponse
from fastapi import HTTPException
from sqlalchemy.orm import Session
import uuid
import io
import csv
import datetime

from exceptions import InvalidTimeException
from dependencies import get_db
from core import read_all_rides, read_shifts, read_ride_status_by_id, read_cancel_reason_by_id



csv_export_router = APIRouter()


@csv_export_router.get(
    "/csv-export/rides",
    tags=["CSV Export"],
    status_code=200,
    response_class=StreamingResponse
)
async def export_rides_endpoint(
    operator_id: uuid.UUID | None = None,
    ride_status_id: int | None = None,
    glider_name: str | None = None,
    route_id: int | None = None,
    start_time: str | None = None,
    end_time: str | None = None,
    customer_id: int | None = None,
    db: Session = Depends(get_db),
):
    if start_time:
        try:
            start_time = datetime.datetime.fromisoformat(start_time)
        except ValueError:
            raise InvalidTimeException()
    if end_time:
        try:
            end_time = datetime.datetime.fromisoformat(end_time)
        except ValueError:
            raise InvalidTimeException()
    
    rides_to_export = read_all_rides(
        db=db,
        operator_id=operator_id,
        ride_status_id=ride_status_id,
        glider_name=glider_name,
        route_id=route_id,
        start_time=start_time,
        end_time=end_time,
        customer_id=customer_id,
        use_pagination=False
    )

    def iter_csv():
        buffer = io.StringIO()
        writer = csv.writer(buffer)

        # Write header
        writer.writerow(["Ride ID", "Start Location", "End Location", "Departure Time", "Arrival Time", "Ride Status", "Glider Name", "Operator ID", "Has Package?", "Package Description", "Internal Route ID", "Cancel Reason"])
        yield buffer.getvalue()
        buffer.seek(0)
        buffer.truncate(0)

        # Write each row
        for ride in rides_to_export:
            ride_status_name = None
            if ride.ride_status_id:
                ride_status = read_ride_status_by_id(db, ride.ride_status_id)
                if ride_status:
                    ride_status_name = ride_status.name
            cancel_reason_name = None
            if ride.cancel_reason_id:
                cancel_reason = read_cancel_reason_by_id(db, ride.cancel_reason_id)
                if cancel_reason:
                    cancel_reason_name = cancel_reason.name
            internal_route_id = None
            if ride.route_id:
                internal_route_id = ride.route_id
            writer.writerow([ride.id, ride.from_location, ride.to_location, ride.departure_time, ride.arrival_time, ride_status_name, ride.glider_name, ride.operator_id, ride.has_package, ride.package_description, internal_route_id, cancel_reason_name])
            yield buffer.getvalue()
            buffer.seek(0)
            buffer.truncate(0)

    try:
        headers = {
            "Content-Disposition": "attachment; filename=rides.csv"
        }
        return StreamingResponse(iter_csv(), media_type="text/csv", headers=headers)
    except Exception as e:
        raise HTTPException(
            status_code=400,
            detail=f"An error occurred while generating the CSV: {str(e)}"
        )


@csv_export_router.get(
    "/csv-export/shifts",
    tags=["CSV Export"],
    status_code=200,
    response_class=StreamingResponse
)
async def export_shifts_endpoint(
    start_date: str | None = None,
    end_date: str | None = None,
    pilot_email: str | None = None,
    route_id: int | None = None,
    ride_id: int | None = None,
    customer_id: int | None = None,
    db: Session = Depends(get_db)
):
    if start_date:
        try:
            start_date = datetime.date.fromisoformat(start_date)
        except ValueError:
            raise InvalidTimeException()
    if end_date:
        try:
            end_date = datetime.date.fromisoformat(end_date)
        except ValueError:
            raise InvalidTimeException()
    
    shifts_to_export = read_shifts(
        db=db,
        start_date=start_date,
        end_date=end_date,
        pilot_email=pilot_email,
        route_id=route_id,
        ride_id=ride_id,
        customer_id=customer_id,
        use_pagination=False
    )

    def iter_csv():
        buffer = io.StringIO()
        writer = csv.writer(buffer)

        # Write header
        writer.writerow(["Shift ID", "Pilot Email", "Internal Route ID", "Start Time", "Stop Time", "Description", "Ride ID", "Created At", "Updated At"])
        yield buffer.getvalue()
        buffer.seek(0)
        buffer.truncate(0)

        # Write each row
        for shift in shifts_to_export:
            internal_route_id = None
            if shift.route_id:
                internal_route_id = shift.route_id
            ride_id = None
            if shift.ride_id:
                ride_id = shift.ride_id
            writer.writerow([shift.id, shift.pilot_email, internal_route_id, shift.start_time, shift.stop_time, shift.description, ride_id, shift.created_at, shift.updated_at])
            yield buffer.getvalue()
            buffer.seek(0)
            buffer.truncate(0)

    try:
        headers = {
            "Content-Disposition": "attachment; filename=shifts.csv"
        }
        return StreamingResponse(iter_csv(), media_type="text/csv", headers=headers)
    except Exception as e:
        raise HTTPException(
            status_code=400,
            detail=f"An error occurred while generating the CSV: {str(e)}"
        )
