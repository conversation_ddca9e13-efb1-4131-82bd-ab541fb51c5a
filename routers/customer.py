from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from core import CustomerIn, CustomerOut
from dependencies import get_db
from core import create_new_customer, read_customer_by_id, read_all_customers
from exceptions import *

customer_router = APIRouter()

@customer_router.post("/customers", tags=["Customers"], status_code=201, response_model=CustomerOut)
async def add_customer(new_customer: CustomerIn, db: Session = Depends(get_db)):
    try:
        customer = create_new_customer(db, new_customer)
        return customer
    except Exception as e:
        print(e)
        raise UndefinedException()

@customer_router.get("/customers", tags=["Customers"], status_code=200, response_model=list[CustomerOut])
async def get_customers(skip: int = 0, limit: int = 10, db: Session = Depends(get_db)):
    return read_all_customers(db, skip=skip, limit=limit)

@customer_router.get("/customers/{customer_id}", tags=["Customers"], status_code=200, response_model=CustomerOut)
async def get_customer(customer_id: int, db: Session = Depends(get_db)):
    customer = read_customer_by_id(db, customer_id)
    if customer is None:
        raise CustomerNotFoundException(customer_id=customer_id)
    return customer 