from fastapi import APIRouter, Depends
from fastapi.exceptions import HTTPException
from sqlalchemy.orm import Session
import datetime
import uuid
from core import RideIn, RideOut, RideUpdate, CancelReasonIn, CancelReasonOut, RouteIn
from dependencies import get_db
from core import create_new_ride, create_cancel_reason, read_ride_by_id, read_all_rides, read_rides_today, read_all_cancel_reasons, read_route_id_from_location_ids, create_new_route
from exceptions import *
from util import *


ride_router = APIRouter()


@ride_router.post("/rides", tags=["Rides"], status_code=201, response_model=RideOut)
async def add_ride(new_ride: RideIn, db: Session = Depends(get_db)):
    # Check that departure_time is before arrival_time
    if new_ride.departure_time > new_ride.arrival_time:
        raise InvalidTimeException()

    # Check that ride_status_id is a valid ride status
    if not is_valid_ride_status(db, new_ride.ride_status_id):
        raise InvalidRideStatusException()

    # Check that if has_package is True, package_description is not None
    if new_ride.has_package and new_ride.package_description is None:
        raise MissingPackageDescriptionException()
    
    # If route id is null, match with location ids
    if new_ride.route_id is None:
        if new_ride.from_location is None or new_ride.to_location is None:
            # TODO: Replace with Warning, connect to ms-glider, and add locations
            raise HTTPException(
                status_code=400,
                detail="If route_id is null, from_location and to_location must be provided.",
            )
        # Create a new route based on the locations
        corresponding_route = read_route_id_from_location_ids(
            db,
            start_location_id=new_ride.from_location,
            end_location_id=new_ride.to_location,
        )
        if corresponding_route is not None:
            new_ride.route_id = corresponding_route.id
        else: # If route_id is still None, raise an error
            # TODO: Connect to ms-glider and fill out all locations if necessary
            print("Warning: No route found for the provided locations. Adding new route with minimal information...")
            new_route = create_new_route(db, RouteIn(
                start_location_id=new_ride.from_location,
                start_location_name=None,
                end_location_id=new_ride.to_location,
                end_location_name=None,
                emergency_contact="",
                known_dangers="",
                extra_notes="",
                customer_id=None
            ))
            new_ride.route_id = new_route.id

    # try to create the new ride and raise an internal server error if it fails
    try:
        new_ride = create_new_ride(db, new_ride)
        return new_ride
    except Exception as e:
        print(e)
        raise UndefinedException()


@ride_router.patch(
    "/rides/{ride_id}", tags=["Rides"], status_code=200, response_model=RideOut
)
async def update_ride(
    ride_id: int, ride_update: RideUpdate, db: Session = Depends(get_db)
):
    # Get the ride by id
    ride = read_ride_by_id(db, ride_id)
    if ride is None:
        raise RideNotFoundException(ride_id=ride_id)

    # Update the fields
    if ride_update.route_id is not None:
        ride.route_id = ride_update.route_id

    if ride_update.ride_status_id is not None:
        if not is_valid_ride_status(db, ride_update.ride_status_id):
            db.rollback()
            raise InvalidRideStatusException()
        ride.ride_status_id = ride_update.ride_status_id

    if ride_update.has_package is not None:
        ride.has_package = ride_update.has_package

    if ride_update.package_description is not None:
        if ride_update.has_package and ride_update.package_description is None:
            db.rollback()
            raise MissingPackageDescriptionException()
        ride.package_description = ride_update.package_description

    if ride_update.departure_time is not None:
        ride.departure_time = ride_update.departure_time

    if ride_update.arrival_time is not None:
        ride.arrival_time = ride_update.arrival_time

    # TODO: Check that the glider_id and glider_name are valid
    if ride_update.glider_id is not None and ride_update.glider_name is not None:
        ride.glider_id = ride_update.glider_id
        ride.glider_name = ride_update.glider_name

    if ride_update.operator_id is not None:
        ride.operator_id = ride_update.operator_id

    if ride_update.cancel_reason_id is not None:
        ride.cancel_reason_id = ride_update.cancel_reason_id

    if ride.arrival_time < ride.departure_time:
        db.rollback()
        raise InvalidTimeException()

    # Commit the changes
    db.commit()

    return ride


@ride_router.get(
    "/rides", tags=["Rides"], status_code=200, response_model=list[RideOut]
)
async def get_rides(
    skip: int = 0,
    limit: int = 10,
    operator_id: uuid.UUID | None = None,
    ride_status_id: int | None = None,
    glider_name: str | None = None,
    route_id: int | None = None,
    start_time: str | None = None,
    end_time: str | None = None,
    customer_id: int | None = None,
    db: Session = Depends(get_db),
):
    if start_time:
        try:
            start_time = datetime.datetime.fromisoformat(start_time)
        except ValueError:
            raise InvalidTimeException()
    if end_time:
        try:
            end_time = datetime.datetime.fromisoformat(end_time)
        except ValueError:
            raise InvalidTimeException()
        
    return read_all_rides(
        db, 
        skip=skip, 
        limit=limit, 
        operator_id=operator_id, 
        ride_status_id=ride_status_id, 
        glider_name=glider_name, 
        route_id=route_id, 
        start_time=start_time,
        end_time=end_time,
        customer_id=customer_id,
    )


@ride_router.get(
    "/rides/today", tags=["Rides"], status_code=200, response_model=list[RideOut]
)
async def get_rides_today(
    skip: int = 0,
    limit: int = 10,
    operator_id: uuid.UUID | None = None,
    only_pending_and_flying: int = 0,
    db: Session = Depends(get_db),
):
    return read_rides_today(
        db,
        skip=skip,
        limit=limit,
        operator_id=operator_id,
        only_pending_and_flying=only_pending_and_flying,
    )


@ride_router.get(
    "/rides/cancel-reasons", tags=["Rides"], status_code=200, response_model=list[CancelReasonOut]
)
async def get_cancel_reasons(db: Session = Depends(get_db)):
    cancel_reasons = read_all_cancel_reasons(db)
    if cancel_reasons is None:
        raise HTTPException(
            status_code=404,
            detail="No cancel reasons found",
        )

    return cancel_reasons

@ride_router.post(
    "/rides/cancel-reasons", tags=["Rides"], status_code=201, response_model=CancelReasonOut
)
async def add_cancel_reason(
    new_cancel_reason: CancelReasonIn, db: Session = Depends(get_db)
):
    # Check that the cancel reason name is not empty
    if new_cancel_reason.name == "":
        raise HTTPException(
            status_code=400,
            detail="Cancel reason name cannot be empty",
        )

    # try to create the new cancel reason and raise an internal server error if it fails
    try:
        new_cancel_reason = create_cancel_reason(db, new_cancel_reason)
        return new_cancel_reason
    except Exception as e:
        print(e)
        raise UndefinedException()

@ride_router.get(
    "/rides/{ride_id}", tags=["Rides"], status_code=200, response_model=RideOut
)
async def get_ride(ride_id: int, db: Session = Depends(get_db)):
    ride = read_ride_by_id(db, ride_id)
    if ride is None:
        raise RideNotFoundException(ride_id=ride_id)

    return ride


@ride_router.delete(
    "/rides/{ride_id}", tags=["Rides"], status_code=204, response_model=None
)
async def delete_ride(ride_id: int, db: Session = Depends(get_db)):
    # Get the ride by id
    ride = read_ride_by_id(db, ride_id)

    if ride is None:
        raise RideNotFoundException(ride_id=ride_id)
    
    try:
        # Delete the ride
        db.delete(ride)
        db.commit()
    except Exception as e:
        print(e)
        db.rollback()
        raise RideNotFoundException(ride_id=ride_id)

    print(f"Ride {ride_id} deleted successfully.")
    return None