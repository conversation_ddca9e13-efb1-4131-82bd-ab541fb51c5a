from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from datetime import datetime
from core.models.tables import TimeTrackingEventType
from core.schemas.schemas import TimeTrackingEventCreate, TimeTrackingEventOut, ShiftUpdate
from core.crud.create import create_shift_table_entry_and_start_tracking
from core.crud.read import get_shift_table_entry_by_id, read_last_shift
from core.crud.update import update_shift_table_entry
from core.crud.time_tracking import create_time_tracking_event, get_active_time_tracking, get_active_time_tracking_by_pilot_email
from dependencies import get_db


time_tracking_router = APIRouter()

@time_tracking_router.post("/time-tracking/start", tags=["Time Tracking"], response_model=TimeTrackingEventOut)
async def start_time_tracking(
    route_id: int, 
    pilot_email: str, 
    description: str | None = None,
    ride_id: int | None = None,
    db: Session = Depends(get_db)
):
    # Check if pilot has any active time tracking
    active_tracking = get_active_time_tracking_by_pilot_email(db, pilot_email)
    if active_tracking:
        raise HTTPException(
            status_code=400,
            detail=f"Active time tracking found for route {active_tracking.route_id}. Please stop it first."
        )
    
    if description is None and ride_id is None:
        raise HTTPException(
            status_code=400,
            detail="Either description or ride_id must be provided"
        )
    
    try:
        current_time = datetime.utcnow()
        new_shift, result = create_shift_table_entry_and_start_tracking(
            db, 
            pilot_email=pilot_email,
            route_id=route_id,
            start_time=current_time,
            stop_time=None,
            description=description,
            ride_id=ride_id
        )

    except Exception as e:
        raise HTTPException(
            status_code=400,
            detail=f"An error occurred while starting time tracking: {str(e)}"
        )
    
    return result

@time_tracking_router.post("/time-tracking/stop", tags=["Time Tracking"], response_model=TimeTrackingEventOut)
async def stop_time_tracking(
    shift_id: int, 
    event_timestamp: datetime | None = None,
    db: Session = Depends(get_db)
):
    # Check if there's an active time tracking for this route
    active_tracking = get_active_time_tracking(db, shift_id)
    if not active_tracking:
        raise HTTPException(
            status_code=400,
            detail="No active time tracking found for this route"
        )
    
    pilot_email = active_tracking.pilot_email
    route_id = active_tracking.route_id

    try:
        current_time = datetime.utcnow()
        

        started_shift  = read_last_shift(db, pilot_email)
        if not started_shift:
            raise HTTPException(
                status_code=400,
                detail="No started shift found for this pilot"
            )
        
        event = TimeTrackingEventCreate(
            route_id=route_id,
            pilot_email=pilot_email,
            event=TimeTrackingEventType.STOP,
            shift_id=started_shift.id,
            event_timestamp=event_timestamp if event_timestamp else current_time
        )

        result = create_time_tracking_event(db, event, with_commit=False) 

        # Update the shift with the stop time
        updated_shift = update_shift_table_entry(
            db, 
            shift_id=started_shift.id, 
            update_data=ShiftUpdate(stop_time = event_timestamp if event_timestamp else current_time)
        ) # This function does db.commit()
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=400,
            detail=f"An error occurred while stopping time tracking: {str(e)}"
        )
    
    return result


@time_tracking_router.post("/time-tracking/edit", tags=["Time Tracking"], response_model=TimeTrackingEventOut)
async def edit_time_tracking(
    shift_id: int,
    edit_info: ShiftUpdate,
    event_timestamp: datetime | None = None,
    db: Session = Depends(get_db)
):
    # Check if there is a shift for this route, pilot, and start/stop times
    corresponding_shift = get_shift_table_entry_by_id(db, shift_id) 
        
    if not corresponding_shift:
        raise HTTPException(
            status_code=400,
            detail="No corresponding shift found for this route, pilot, and start/stop times"
        )
    
    route_id = corresponding_shift.route_id
    pilot_email = corresponding_shift.pilot_email
    
    result = None
    try:
        current_time = datetime.utcnow()
        event = TimeTrackingEventCreate(
            route_id=route_id,
            pilot_email=pilot_email,
            event=TimeTrackingEventType.EDIT,
            shift_id=corresponding_shift.id,
            event_timestamp=event_timestamp if event_timestamp else current_time
        )

        result = create_time_tracking_event(db, event, with_commit=False)

        # Update the shift with the new information
        edited_shift = update_shift_table_entry(
            db, 
            shift_id=corresponding_shift.id, 
            update_data=edit_info
        ) # This function does db.commit()

        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=400,
            detail=f"An error occurred while editing time tracking: {str(e)}"
        )
    
    return result
