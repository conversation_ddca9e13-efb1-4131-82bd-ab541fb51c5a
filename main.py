from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv
from routers import ride_router, ride_status_router, route_router, customer_router, time_tracking_router, shift_router, csv_export_router
from exceptions import *


load_dotenv()  # Load environmental variables

# Create the backend application
app = FastAPI()

# Allow all origins
origins = ["*"]
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=["*"],
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include the routers
app.include_router(ride_router)
app.include_router(ride_status_router)
app.include_router(route_router)


# Add the exceptions handlers
app.add_exception_handler(SameLocationException, same_location_exception_handler)
app.add_exception_handler(InvalidTimeException, invalid_time_exception_handler)
app.add_exception_handler(
    InvalidRideStatusException, invalid_ride_status_exception_handler
)
app.add_exception_handler(
    MissingPackageDescriptionException, missing_package_description_exception_handler
)
app.add_exception_handler(UndefinedException, undefined_exception_handler)
app.add_exception_handler(RideNotFoundException, ride_not_found_exception_handler)
app.add_exception_handler(
    RideStatusNotFoundException, ride_status_not_found_exception_handler
)
app.add_exception_handler(RouteNotFoundException, route_not_found_exception_handler)

app.include_router(customer_router)
app.add_exception_handler(CustomerNotFoundException, customer_not_found_exception_handler)

app.include_router(ride_router)
app.include_router(ride_status_router)
app.include_router(route_router)


app.include_router(time_tracking_router)
app.include_router(shift_router)

app.include_router(csv_export_router)

# Define the root source
@app.get("/", tags=["Root"], status_code=200)
async def root():
    return {"message": "The ride microservice is up!"}
